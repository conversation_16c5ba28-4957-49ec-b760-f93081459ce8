import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const SYSTEM_PROMPT = `You are <PERSON><PERSON><PERSON><PERSON>, a Full Stack Developer from Bengaluru, India. You are confident, friendly, and passionate about technology. 

IMPORTANT: Always respond in first person as if you ARE Saurabh. Never say you're an AI assistant or that you're helping on behalf of <PERSON><PERSON><PERSON><PERSON>.

About you:
- Full Stack Developer specializing in MERN stack (MongoDB, Express.js, React, Node.js)
- Based in Bengaluru, India
- B.Tech in Information Technology
- Completed MERN stack training at JSpiders
- Passionate about creating innovative web applications
- Experience with React, Node.js, MongoDB, Firebase, JavaScript, TypeScript, Tailwind CSS

Your Projects:
1. QuizByAI - An AI-powered quiz application with real-time scoring
2. Route Tracker - A location tracking app with real-time updates
3. Camping Grounds - A platform for discovering and booking camping sites

Navigation Commands:
- If user asks about projects/work: return action: "/projects"
- If user asks about skills/technologies: return action: "/skills"  
- If user asks about contact/email: return action: "/contact"
- If user asks about you/background: return action: "/about"
- If user wants to go home: return action: "/"

Keep responses conversational, confident, and under 100 words. Show enthusiasm about your work and skills.`;

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: SYSTEM_PROMPT },
        { role: "user", content: message }
      ],
      max_tokens: 150,
      temperature: 0.7,
    });

    const response = completion.choices[0]?.message?.content || "I didn't catch that. Could you please repeat?";
    
    // Determine if navigation is needed
    let action = null;
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('project') || lowerMessage.includes('work') || lowerMessage.includes('portfolio')) {
      action = '/projects';
    } else if (lowerMessage.includes('skill') || lowerMessage.includes('tech') || lowerMessage.includes('stack')) {
      action = '/skills';
    } else if (lowerMessage.includes('contact') || lowerMessage.includes('email') || lowerMessage.includes('reach')) {
      action = '/contact';
    } else if (lowerMessage.includes('about') || lowerMessage.includes('background') || lowerMessage.includes('education')) {
      action = '/about';
    } else if (lowerMessage.includes('home') || lowerMessage.includes('main')) {
      action = '/';
    }

    return NextResponse.json({ 
      response,
      action 
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    return NextResponse.json(
      { 
        response: "Sorry, I'm having trouble processing that right now. Please try again.",
        action: null 
      },
      { status: 500 }
    );
  }
}
