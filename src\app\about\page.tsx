'use client';

import { motion } from 'framer-motion';
import { GraduationCap, MapPin, Code, Heart } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-6">
            About Me
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Passionate developer crafting digital experiences with modern technologies
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Bio */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl">
              <h2 className="text-3xl font-bold text-white mb-6">Hi, I'm Saurabh! 👋</h2>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                I'm a passionate Full Stack Developer based in Bengaluru, India, with a strong foundation in 
                modern web technologies. My journey in tech began with curiosity and has evolved into a 
                deep love for creating innovative solutions that make a real impact.
              </p>
              <p className="text-gray-300 text-lg leading-relaxed">
                I specialize in the MERN stack and enjoy building scalable, user-friendly applications. 
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source 
                projects, or sharing knowledge with the developer community.
              </p>
            </div>

            {/* Quick Facts */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="glass p-6 rounded-xl"
              >
                <MapPin className="text-blue-400 mb-3" size={24} />
                <h3 className="text-white font-semibold mb-2">Location</h3>
                <p className="text-gray-300">Bengaluru, India</p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="glass p-6 rounded-xl"
              >
                <Code className="text-purple-400 mb-3" size={24} />
                <h3 className="text-white font-semibold mb-2">Focus</h3>
                <p className="text-gray-300">Full Stack Development</p>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column - Education & Experience */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Education */}
            <div className="glass p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <GraduationCap className="text-blue-400 mr-3" size={28} />
                <h2 className="text-2xl font-bold text-white">Education</h2>
              </div>
              
              <div className="space-y-6">
                <div className="border-l-2 border-blue-400 pl-6">
                  <h3 className="text-xl font-semibold text-white mb-2">
                    B.Tech in Information Technology
                  </h3>
                  <p className="text-gray-300 mb-2">University/College Name</p>
                  <p className="text-gray-400 text-sm">2020 - 2024</p>
                </div>

                <div className="border-l-2 border-purple-400 pl-6">
                  <h3 className="text-xl font-semibold text-white mb-2">
                    MERN Stack Training
                  </h3>
                  <p className="text-gray-300 mb-2">JSpiders</p>
                  <p className="text-gray-400 text-sm">2023</p>
                  <p className="text-gray-300 text-sm mt-2">
                    Comprehensive training in MongoDB, Express.js, React, and Node.js
                  </p>
                </div>
              </div>
            </div>

            {/* Philosophy */}
            <div className="glass p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <Heart className="text-red-400 mr-3" size={28} />
                <h2 className="text-2xl font-bold text-white">My Philosophy</h2>
              </div>
              
              <blockquote className="text-gray-300 text-lg italic leading-relaxed">
                "Technology should be a bridge that connects ideas to reality. 
                I believe in writing clean, efficient code that not only solves problems 
                but also creates delightful user experiences."
              </blockquote>
            </div>
          </motion.div>
        </div>

        {/* Skills Preview */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <h2 className="text-3xl font-bold text-white mb-8">What I Work With</h2>
          <div className="flex flex-wrap justify-center gap-4">
            {['React', 'Node.js', 'MongoDB', 'Express.js', 'JavaScript', 'TypeScript', 'Tailwind CSS', 'Firebase'].map((tech, index) => (
              <motion.span
                key={tech}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8 + index * 0.1 }}
                whileHover={{ scale: 1.1 }}
                className="glass px-4 py-2 rounded-full text-white font-medium"
              >
                {tech}
              </motion.span>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
