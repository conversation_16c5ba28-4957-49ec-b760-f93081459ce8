'use client';

import { motion } from 'framer-motion';
import ProjectCard from '@/components/ProjectCard';

const projects = [
  {
    id: 1,
    title: 'QuizByAI',
    description: 'An AI-powered quiz application with real-time scoring and adaptive difficulty. Built with React, Node.js, and OpenAI API.',
    image: '/projects/quiz-ai.svg',
    technologies: ['React', 'Node.js', 'OpenAI API', 'MongoDB', 'Socket.io'],
    liveUrl: 'https://quiz-by-ai.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/quiz-by-ai',
    featured: true,
  },
  {
    id: 2,
    title: 'Route Tracker',
    description: 'A real-time location tracking application with route optimization and live updates. Perfect for delivery services and logistics.',
    image: '/projects/route-tracker.svg',
    technologies: ['React Native', 'Node.js', 'MongoDB', 'Google Maps API', 'Socket.io'],
    liveUrl: 'https://route-tracker.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/route-tracker',
    featured: true,
  },
  {
    id: 3,
    title: 'Camping Grounds',
    description: 'A comprehensive platform for discovering and booking camping sites with reviews, ratings, and interactive maps.',
    image: '/projects/camping-grounds.svg',
    technologies: ['React', 'Express.js', 'MongoDB', 'Mapbox', 'Cloudinary'],
    liveUrl: 'https://camping-grounds.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/camping-grounds',
    featured: false,
  },
  {
    id: 4,
    title: 'E-Commerce Dashboard',
    description: 'A modern admin dashboard for e-commerce management with analytics, inventory tracking, and order management.',
    image: '/projects/ecommerce-dashboard.svg',
    technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Chart.js'],
    liveUrl: 'https://ecommerce-dashboard.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/ecommerce-dashboard',
    featured: false,
  },
  {
    id: 5,
    title: 'Weather App',
    description: 'A beautiful weather application with location-based forecasts, interactive maps, and weather alerts.',
    image: '/projects/weather-app.svg',
    technologies: ['React', 'TypeScript', 'OpenWeather API', 'Tailwind CSS'],
    liveUrl: 'https://weather-app-saurabh.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/weather-app',
    featured: false,
  },
  {
    id: 6,
    title: 'Task Management System',
    description: 'A collaborative task management platform with team features, deadlines, and progress tracking.',
    image: '/projects/task-management.svg',
    technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Socket.io', 'JWT'],
    liveUrl: 'https://task-management.vercel.app',
    githubUrl: 'https://github.com/saurabh-dahariya/task-management',
    featured: false,
  },
];

export default function Projects() {
  const featuredProjects = projects.filter(project => project.featured);
  const otherProjects = projects.filter(project => !project.featured);

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-6">
            My Projects
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A showcase of my work, from AI-powered applications to full-stack web solutions
          </p>
        </motion.div>

        {/* Featured Projects */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-20"
        >
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Featured Projects</h2>
          <div className="grid lg:grid-cols-2 gap-8">
            {featuredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }}
              >
                <ProjectCard project={project} featured />
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Other Projects */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Other Projects</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 + index * 0.1 }}
              >
                <ProjectCard project={project} />
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center mt-20"
        >
          <div className="glass p-8 rounded-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Interested in working together?
            </h3>
            <p className="text-gray-300 mb-6">
              I'm always open to discussing new opportunities and exciting projects.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-3 rounded-full text-white font-semibold hover:shadow-lg transition-all duration-300"
              onClick={() => window.location.href = '/contact'}
            >
              Get In Touch
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
