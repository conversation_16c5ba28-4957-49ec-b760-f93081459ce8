'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

interface Skill {
  name: string;
  level: number;
  description: string;
}

interface SkillCardProps {
  skill: Skill;
}

export default function SkillCard({ skill }: SkillCardProps) {
  const [animatedLevel, setAnimatedLevel] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedLevel(skill.level);
    }, 500);

    return () => clearTimeout(timer);
  }, [skill.level]);

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -5 }}
      className="glass p-6 rounded-xl h-full"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{skill.name}</h3>
        <span className="text-blue-400 font-bold text-sm">{skill.level}%</span>
      </div>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 mb-4 overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${animatedLevel}%` }}
          transition={{ duration: 1.5, ease: 'easeOut' }}
        />
      </div>
      
      <p className="text-gray-300 text-sm leading-relaxed">{skill.description}</p>
    </motion.div>
  );
}
