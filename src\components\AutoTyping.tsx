'use client';

import { useState, useEffect } from 'react';

interface AutoTypingProps {
  text: string;
  speed?: number;
  className?: string;
  showCursor?: boolean;
}

export default function AutoTyping({ 
  text, 
  speed = 100, 
  className = '', 
  showCursor = true 
}: AutoTypingProps) {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showCursorBlink, setShowCursorBlink] = useState(true);

  useEffect(() => {
    setDisplayText('');
    setCurrentIndex(0);
  }, [text]);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timer);
    }
  }, [currentIndex, text, speed]);

  useEffect(() => {
    const cursorTimer = setInterval(() => {
      setShowCursorBlink(prev => !prev);
    }, 500);

    return () => clearInterval(cursorTimer);
  }, []);

  return (
    <span className={className}>
      {displayText}
      {showCursor && (
        <span 
          className={`inline-block w-1 ml-1 ${showCursorBlink ? 'bg-white' : 'bg-transparent'}`}
          style={{ height: '1em' }}
        />
      )}
    </span>
  );
}
