# 🚀 Saura<PERSON>h Dahariya - AI-Powered Portfolio

A futuristic, interactive portfolio website featuring an AI voice assistant, macOS-style design, and modern web technologies.

## ✨ Features

### 🎙️ AI Voice Assistant
- **Voice Commands**: Click the mic button and speak naturally
- **Smart Navigation**: "Show me your projects" → automatically navigates to projects page
- **Text-to-Speech**: AI responds with voice using Web Speech API
- **Chat Interface**: Alternative text-based interaction
- **Glowing Effects**: Visual feedback with animated borders during voice interaction

### 🎨 macOS-Style Design
- **Glassmorphism UI**: Frosted glass effects throughout
- **Smooth Animations**: Framer Motion powered transitions
- **Auto-typing Hero**: Dynamic text animation on homepage
- **Infinite Tech Carousel**: Scroll-direction controlled logo animation
- **Responsive Design**: Perfect on all devices

### 📱 Interactive Sections
- **Home**: Auto-typing intro with floating particles
- **About**: Personal story with education timeline
- **Projects**: Hover previews with live demo links
- **Skills**: Animated progress bars and infinite scrolling logos
- **Contact**: Glassmorphic contact form with social links

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Framer Motion** - Smooth animations
- **Lucide React** - Modern icons

### AI Integration
- **OpenAI GPT-4** - Intelligent responses
- **Web Speech API** - Voice recognition and synthesis
- **Custom AI Personality** - Responds as Saurabh in first person

### Deployment
- **Vercel** - Optimized hosting
- **Edge Functions** - Fast API responses

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/saurabh-dahariya/portfolio-saurabh.git
cd portfolio-saurabh
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
Create a `.env.local` file:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

4. **Run the development server**
```bash
npm run dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

### Voice Assistant Commands
- "Show me your projects" → Navigate to projects
- "What are your skills?" → Navigate to skills
- "Tell me about yourself" → Navigate to about
- "How can I contact you?" → Navigate to contact
- "Take me home" → Navigate to homepage

### Navigation
- Use the glassmorphic navbar for quick navigation
- Scroll to see infinite tech logo animations
- Hover over project cards for previews
- Click the chat icon for text-based AI interaction

## 🎨 Customization

### Colors & Themes
Edit `tailwind.config.js` for custom colors:
```js
colors: {
  'glass': {
    'light': 'rgba(255, 255, 255, 0.1)',
    'medium': 'rgba(255, 255, 255, 0.2)',
  },
}
```

### AI Personality
Modify the system prompt in `src/app/api/chat/route.ts`:
```typescript
const SYSTEM_PROMPT = `You are Saurabh Dahariya...`
```

### Projects
Update project data in `src/app/projects/page.tsx`:
```typescript
const projects = [
  {
    title: 'Your Project',
    description: 'Project description',
    // ... other fields
  }
]
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── about/             # About page
│   ├── projects/          # Projects showcase
│   ├── skills/            # Skills & technologies
│   ├── contact/           # Contact form
│   └── api/chat/          # AI chat endpoint
├── components/            # Reusable components
│   ├── VoiceAssistant.tsx # AI voice features
│   ├── Navbar.tsx         # Navigation
│   ├── HeroSection.tsx    # Homepage hero
│   └── ...
├── types/                 # TypeScript definitions
└── styles/               # Global styles
```

## 🌟 Key Components

### VoiceAssistant
- Speech recognition and synthesis
- AI chat integration
- Visual feedback effects

### SkillsCarousel
- Infinite scrolling tech logos
- Scroll-direction based animation
- Hover effects with glow

### ProjectCard
- Hover preview functionality
- Glassmorphic design
- Live demo and GitHub links

## 🚀 Deployment

### Vercel (Recommended)
1. Push to GitHub
2. Connect to Vercel
3. Add environment variables
4. Deploy automatically

### Manual Deployment
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/saurabh-dahariya](https://linkedin.com/in/saurabh-dahariya)
- **GitHub**: [github.com/saurabh-dahariya](https://github.com/saurabh-dahariya)

---

Built with ❤️ by Saurabh Dahariya using Next.js, OpenAI, and modern web technologies.
