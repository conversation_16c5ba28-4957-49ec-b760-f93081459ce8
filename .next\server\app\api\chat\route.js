/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_main_project_portfolio_saurabh_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_main_project_portfolio_saurabh_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nconst SYSTEM_PROMPT = `You are Saurabh Dahariya, a Full Stack Developer from Bengaluru, India. You are confident, friendly, and passionate about technology. \n\nIMPORTANT: Always respond in first person as if you ARE Saurabh. Never say you're an AI assistant or that you're helping on behalf of Saurabh.\n\nAbout you:\n- Full Stack Developer specializing in MERN stack (MongoDB, Express.js, React, Node.js)\n- Based in Bengaluru, India\n- B.Tech in Information Technology\n- Completed MERN stack training at JSpiders\n- Passionate about creating innovative web applications\n- Experience with React, Node.js, MongoDB, Firebase, JavaScript, TypeScript, Tailwind CSS\n\nYour Projects:\n1. QuizByAI - An AI-powered quiz application with real-time scoring\n2. Route Tracker - A location tracking app with real-time updates\n3. Camping Grounds - A platform for discovering and booking camping sites\n\nNavigation Commands:\n- If user asks about projects/work: return action: \"/projects\"\n- If user asks about skills/technologies: return action: \"/skills\"  \n- If user asks about contact/email: return action: \"/contact\"\n- If user asks about you/background: return action: \"/about\"\n- If user wants to go home: return action: \"/\"\n\nKeep responses conversational, confident, and under 100 words. Show enthusiasm about your work and skills.`;\nasync function POST(request) {\n    try {\n        const { message } = await request.json();\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-4\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: SYSTEM_PROMPT\n                },\n                {\n                    role: \"user\",\n                    content: message\n                }\n            ],\n            max_tokens: 150,\n            temperature: 0.7\n        });\n        const response = completion.choices[0]?.message?.content || \"I didn't catch that. Could you please repeat?\";\n        // Determine if navigation is needed\n        let action = null;\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes('project') || lowerMessage.includes('work') || lowerMessage.includes('portfolio')) {\n            action = '/projects';\n        } else if (lowerMessage.includes('skill') || lowerMessage.includes('tech') || lowerMessage.includes('stack')) {\n            action = '/skills';\n        } else if (lowerMessage.includes('contact') || lowerMessage.includes('email') || lowerMessage.includes('reach')) {\n            action = '/contact';\n        } else if (lowerMessage.includes('about') || lowerMessage.includes('background') || lowerMessage.includes('education')) {\n            action = '/about';\n        } else if (lowerMessage.includes('home') || lowerMessage.includes('main')) {\n            action = '/';\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response,\n            action\n        });\n    } catch (error) {\n        console.error('OpenAI API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: \"Sorry, I'm having trouble processing that right now. Please try again.\",\n            action: null\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();