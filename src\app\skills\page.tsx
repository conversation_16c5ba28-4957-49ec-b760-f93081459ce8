'use client';

import { motion } from 'framer-motion';
import SkillsCarousel from '@/components/SkillsCarousel';
import SkillCard from '@/components/SkillCard';

const skillCategories = [
  {
    title: 'Frontend Development',
    icon: '🎨',
    skills: [
      { name: 'React', level: 90, description: 'Building dynamic user interfaces with hooks and context' },
      { name: 'Next.js', level: 85, description: 'Server-side rendering and static site generation' },
      { name: 'TypeScript', level: 80, description: 'Type-safe JavaScript development' },
      { name: 'Tailwind CSS', level: 95, description: 'Utility-first CSS framework for rapid UI development' },
      { name: 'JavaScript', level: 90, description: 'Modern ES6+ features and async programming' },
      { name: 'HTML5 & CSS3', level: 95, description: 'Semantic markup and responsive design' },
    ]
  },
  {
    title: 'Backend Development',
    icon: '⚙️',
    skills: [
      { name: 'Node.js', level: 85, description: 'Server-side JavaScript runtime and API development' },
      { name: 'Express.js', level: 80, description: 'Fast and minimalist web framework' },
      { name: 'MongoDB', level: 75, description: 'NoSQL database design and optimization' },
      { name: 'Firebase', level: 70, description: 'Real-time database and authentication' },
      { name: 'REST APIs', level: 85, description: 'RESTful service design and implementation' },
      { name: 'JWT', level: 75, description: 'JSON Web Token authentication' },
    ]
  },
  {
    title: 'Tools & Technologies',
    icon: '🛠️',
    skills: [
      { name: 'Git & GitHub', level: 85, description: 'Version control and collaborative development' },
      { name: 'VS Code', level: 90, description: 'Efficient development environment setup' },
      { name: 'Postman', level: 80, description: 'API testing and documentation' },
      { name: 'Vercel', level: 75, description: 'Deployment and hosting solutions' },
      { name: 'Figma', level: 70, description: 'UI/UX design and prototyping' },
      { name: 'Chrome DevTools', level: 85, description: 'Debugging and performance optimization' },
    ]
  }
];

export default function Skills() {
  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-6">
            My Skills
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Technologies and tools I use to bring ideas to life
          </p>
        </motion.div>

        {/* Infinite Scrolling Tech Logos */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-20"
        >
          <SkillsCarousel />
        </motion.section>

        {/* Skill Categories */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="space-y-16"
        >
          {skillCategories.map((category, categoryIndex) => (
            <div key={category.title}>
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.5 + categoryIndex * 0.1 }}
                className="flex items-center mb-8"
              >
                <span className="text-4xl mr-4">{category.icon}</span>
                <h2 className="text-3xl font-bold text-white">{category.title}</h2>
              </motion.div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      duration: 0.8, 
                      delay: 0.6 + categoryIndex * 0.1 + skillIndex * 0.05 
                    }}
                  >
                    <SkillCard skill={skill} />
                  </motion.div>
                ))}
              </div>
            </div>
          ))}
        </motion.section>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 text-center"
        >
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Always Learning, Always Growing
            </h3>
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              The tech world evolves rapidly, and I'm committed to staying current with the latest 
              technologies and best practices. I regularly explore new frameworks, contribute to 
              open-source projects, and participate in the developer community.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <span className="bg-blue-500/20 text-blue-400 px-4 py-2 rounded-full text-sm">
                Currently Learning: AI/ML
              </span>
              <span className="bg-purple-500/20 text-purple-400 px-4 py-2 rounded-full text-sm">
                Next Goal: Cloud Architecture
              </span>
              <span className="bg-green-500/20 text-green-400 px-4 py-2 rounded-full text-sm">
                Exploring: Web3 Technologies
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
