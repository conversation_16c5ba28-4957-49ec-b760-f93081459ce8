'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const technologies = [
  { name: 'React', icon: '⚛️', color: '#61DAFB' },
  { name: 'Node.js', icon: '🟢', color: '#339933' },
  { name: 'MongoDB', icon: '🍃', color: '#47A248' },
  { name: 'Express.js', icon: '🚀', color: '#000000' },
  { name: 'JavaScript', icon: '📜', color: '#F7DF1E' },
  { name: 'TypeScript', icon: '📘', color: '#3178C6' },
  { name: 'Next.js', icon: '▲', color: '#000000' },
  { name: 'Tailwind CSS', icon: '🎨', color: '#06B6D4' },
  { name: 'Firebase', icon: '🔥', color: '#FFCA28' },
  { name: 'Git', icon: '📚', color: '#F05032' },
  { name: 'VS Code', icon: '💻', color: '#007ACC' },
  { name: 'Figma', icon: '🎯', color: '#F24E1E' },
  { name: 'Vercel', icon: '▲', color: '#000000' },
  { name: 'HTML5', icon: '🌐', color: '#E34F26' },
  { name: 'CSS3', icon: '🎨', color: '#1572B6' },
  { name: 'JWT', icon: '🔐', color: '#000000' },
];

export default function SkillsCarousel() {
  const [scrollDirection, setScrollDirection] = useState<'left' | 'right'>('left');
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY) {
        // Scrolling down - move logos left to right
        setScrollDirection('right');
      } else {
        // Scrolling up - move logos right to left
        setScrollDirection('left');
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  // Duplicate the technologies array for seamless infinite scroll
  const duplicatedTechnologies = [...technologies, ...technologies];

  return (
    <div className="relative overflow-hidden py-12">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
      
      {/* Scrolling container */}
      <motion.div
        className="flex space-x-8"
        animate={{
          x: scrollDirection === 'left' ? [0, -50 * technologies.length] : [-50 * technologies.length, 0],
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        {duplicatedTechnologies.map((tech, index) => (
          <motion.div
            key={`${tech.name}-${index}`}
            className="flex-shrink-0 group"
            whileHover={{ scale: 1.1, y: -5 }}
            transition={{ type: 'spring', stiffness: 300 }}
          >
            <div className="glass p-6 rounded-2xl min-w-[120px] text-center hover:glow-blue transition-all duration-300">
              <div 
                className="text-4xl mb-3 transition-transform duration-300 group-hover:scale-110"
                style={{ filter: `drop-shadow(0 0 8px ${tech.color}40)` }}
              >
                {tech.icon}
              </div>
              <h3 className="text-white font-semibold text-sm">{tech.name}</h3>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Fade edges */}
      <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-[#0f0f23] to-transparent pointer-events-none"></div>
      <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-[#0f0f23] to-transparent pointer-events-none"></div>
      
      {/* Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
        className="text-center mt-8"
      >
        <p className="text-gray-400 text-sm">
          Scroll to see the technologies in action! 
          <span className="block mt-1">↕️ Scroll direction controls animation direction</span>
        </p>
      </motion.div>
    </div>
  );
}
