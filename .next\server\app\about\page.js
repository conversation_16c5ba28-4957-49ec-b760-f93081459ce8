/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNtYWluJTIwcHJvamVjdCU1QyU1Q3BvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYWJvdXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxtYWluIHByb2plY3RcXFxccG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGFwcFxcXFxhYm91dFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(rsc)/./src/components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VoiceAssistant.tsx */ \"(rsc)/./src/components/VoiceAssistant.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNtYWluJTIwcHJvamVjdCU1QyU1Q3BvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q21haW4lMjBwcm9qZWN0JTVDJTVDcG9ydGZvbGlvLXNhdXJhYmglNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTmF2YmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q21haW4lMjBwcm9qZWN0JTVDJTVDcG9ydGZvbGlvLXNhdXJhYmglNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDVm9pY2VBc3Npc3RhbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQWtJO0FBQ2xJO0FBQ0Esa0xBQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcbWFpbiBwcm9qZWN0XFxcXHBvcnRmb2xpby1zYXVyYWJoXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXE5hdmJhci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJEOlxcXFxtYWluIHByb2plY3RcXFxccG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVm9pY2VBc3Npc3RhbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxtYWluIHByb2plY3RcXHBvcnRmb2xpby1zYXVyYWJoXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\main project\\portfolio-saurabh\\src\\app\\about\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e884d43daf3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcbWFpbiBwcm9qZWN0XFxwb3J0Zm9saW8tc2F1cmFiaFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNWU4ODRkNDNkYWYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(rsc)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_VoiceAssistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/VoiceAssistant */ \"(rsc)/./src/components/VoiceAssistant.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Saurabh Dahariya - Full Stack Developer\",\n    description: \"AI-powered portfolio of Saurabh Dahariya, a Full Stack Developer from Bengaluru specializing in MERN stack and modern web technologies.\",\n    keywords: \"Saurabh Dahariya, Full Stack Developer, React, Node.js, MongoDB, Portfolio, AI Assistant\",\n    authors: [\n        {\n            name: \"Saurabh Dahariya\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sf antialiased animated-bg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"relative\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VoiceAssistant__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\main project\\portfolio-saurabh\\src\\components\\Navbar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/VoiceAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/VoiceAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\main project\\portfolio-saurabh\\src\\components\\VoiceAssistant.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(ssr)/./src/app/about/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNtYWluJTIwcHJvamVjdCU1QyU1Q3BvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYWJvdXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQW1HIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxtYWluIHByb2plY3RcXFxccG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGFwcFxcXFxhYm91dFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cabout%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(ssr)/./src/components/Navbar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VoiceAssistant.tsx */ \"(ssr)/./src/components/VoiceAssistant.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNtYWluJTIwcHJvamVjdCU1QyU1Q3BvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q21haW4lMjBwcm9qZWN0JTVDJTVDcG9ydGZvbGlvLXNhdXJhYmglNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDTmF2YmFyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q21haW4lMjBwcm9qZWN0JTVDJTVDcG9ydGZvbGlvLXNhdXJhYmglNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDVm9pY2VBc3Npc3RhbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQWtJO0FBQ2xJO0FBQ0Esa0xBQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcbWFpbiBwcm9qZWN0XFxcXHBvcnRmb2xpby1zYXVyYWJoXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXE5hdmJhci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJEOlxcXFxtYWluIHByb2plY3RcXFxccG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVm9pY2VBc3Npc3RhbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmain%20project%5C%5Cportfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5CVoiceAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,MapPin!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,MapPin!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,MapPin!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,MapPin!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction About() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen pt-20 pb-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-6\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"Passionate developer crafting digital experiences with modern technologies\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 rounded-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-white mb-6\",\n                                            children: \"Hi, I'm Saurabh! \\uD83D\\uDC4B\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-lg leading-relaxed mb-6\",\n                                            children: \"I'm a passionate Full Stack Developer based in Bengaluru, India, with a strong foundation in modern web technologies. My journey in tech began with curiosity and has evolved into a deep love for creating innovative solutions that make a real impact.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-lg leading-relaxed\",\n                                            children: \"I specialize in the MERN stack and enjoy building scalable, user-friendly applications. When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            className: \"glass p-6 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"text-blue-400 mb-3\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-2\",\n                                                    children: \"Location\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Bengaluru, India\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            className: \"glass p-6 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"text-purple-400 mb-3\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-2\",\n                                                    children: \"Focus\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Full Stack Development\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 rounded-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"text-blue-400 mr-3\",\n                                                    size: 28\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Education\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-l-2 border-blue-400 pl-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white mb-2\",\n                                                            children: \"B.Tech in Information Technology\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 mb-2\",\n                                                            children: \"University/College Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"2020 - 2024\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-l-2 border-purple-400 pl-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white mb-2\",\n                                                            children: \"MERN Stack Training\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 mb-2\",\n                                                            children: \"JSpiders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm mt-2\",\n                                                            children: \"Comprehensive training in MongoDB, Express.js, React, and Node.js\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass p-8 rounded-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_GraduationCap_Heart_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"text-red-400 mr-3\",\n                                                    size: 28\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"My Philosophy\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"text-gray-300 text-lg italic leading-relaxed\",\n                                            children: '\"Technology should be a bridge that connects ideas to reality. I believe in writing clean, efficient code that not only solves problems but also creates delightful user experiences.\"'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"mt-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-white mb-8\",\n                            children: \"What I Work With\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4\",\n                            children: [\n                                'React',\n                                'Node.js',\n                                'MongoDB',\n                                'Express.js',\n                                'JavaScript',\n                                'TypeScript',\n                                'Tailwind CSS',\n                                'Firebase'\n                            ].map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        delay: 0.8 + index * 0.1\n                                    },\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    className: \"glass px-4 py-2 rounded-full text-white font-medium\",\n                                    children: tech\n                                }, tech, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/about/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Code,Home,Mail,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Code,Home,Mail,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Code,Home,Mail,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Code,Home,Mail,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Code,Home,Mail,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navItems = [\n    {\n        name: 'Home',\n        href: '/',\n        icon: _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'About',\n        href: '/about',\n        icon: _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Projects',\n        href: '/projects',\n        icon: _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Skills',\n        href: '/skills',\n        icon: _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Contact',\n        href: '/contact',\n        icon: _barrel_optimize_names_Briefcase_Code_Home_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Navbar() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.nav, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'glass backdrop-blur-lg shadow-lg' : 'bg-transparent'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-400\",\n                                    children: \"Saurabh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-purple-400\",\n                                    children: \"Dahariya\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: navItems.map((item)=>{\n                            const Icon = item.icon;\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: `flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${isActive ? 'bg-blue-500/20 text-blue-400 glow-blue' : 'text-gray-300 hover:text-white hover:bg-white/10'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"glass p-2 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 flex flex-col justify-center items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block w-5 h-0.5 bg-white mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block w-5 h-0.5 bg-white mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block w-5 h-0.5 bg-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VoiceAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/VoiceAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction VoiceAssistant() {\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSpeaking, setIsSpeaking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChat, setShowChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const synthRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceAssistant.useEffect\": ()=>{\n            // Initialize speech recognition\n            if (false) {}\n            // Initialize speech synthesis\n            if (false) {}\n        }\n    }[\"VoiceAssistant.useEffect\"], []);\n    const startListening = ()=>{\n        if (recognitionRef.current) {\n            setIsListening(true);\n            recognitionRef.current.start();\n        }\n    };\n    const stopListening = ()=>{\n        if (recognitionRef.current) {\n            recognitionRef.current.stop();\n            setIsListening(false);\n        }\n    };\n    const speak = (text)=>{\n        if (synthRef.current) {\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.rate = 0.9;\n            utterance.pitch = 1;\n            utterance.volume = 0.8;\n            utterance.onstart = ()=>setIsSpeaking(true);\n            utterance.onend = ()=>setIsSpeaking(false);\n            synthRef.current.speak(utterance);\n        }\n    };\n    const handleUserMessage = async (text)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            text,\n            isUser: true,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: text\n                })\n            });\n            const data = await response.json();\n            const aiMessage = {\n                id: (Date.now() + 1).toString(),\n                text: data.response,\n                isUser: false,\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            speak(data.response);\n            // Handle navigation commands\n            if (data.action) {\n                setTimeout(()=>{\n                    router.push(data.action);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleTextSubmit = (e)=>{\n        e.preventDefault();\n        if (inputText.trim()) {\n            handleUserMessage(inputText);\n            setInputText('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 pointer-events-none z-40\",\n                    style: {\n                        border: '4px solid transparent',\n                        borderRadius: '20px',\n                        boxShadow: '0 0 0 4px rgba(59,130,246,0.3), inset 0 0 0 4px rgba(59,130,246,0.3)',\n                        animation: 'glow-border 2s ease-in-out infinite'\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                className: \"fixed bottom-6 right-6 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        onClick: isListening ? stopListening : startListening,\n                        whileHover: {\n                            scale: 1.1\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        className: `relative w-16 h-16 rounded-full shadow-xl text-white flex items-center justify-center transition-all duration-300 ${isListening ? 'bg-red-500 glow-blue animate-pulse' : 'bg-blue-600 hover:bg-blue-700'}`,\n                        children: [\n                            isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 animate-ping rounded-full bg-blue-500 opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 26\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 49\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                        children: isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 flex gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 bg-blue-400 rounded-full animate-wave1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 bg-blue-400 rounded-full animate-wave2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 bg-blue-400 rounded-full animate-wave3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 bg-blue-400 rounded-full animate-wave1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 bg-blue-400 rounded-full animate-wave2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        onClick: ()=>setShowChat(!showChat),\n                        whileHover: {\n                            scale: 1.1\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        className: \"absolute -top-20 left-0 w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full shadow-lg text-white flex items-center justify-center transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: showChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: 400\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: 400\n                    },\n                    className: \"fixed bottom-6 right-24 w-80 h-96 glass rounded-2xl shadow-2xl z-40 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-white font-semibold\",\n                                    children: \"Chat with Saurabh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowChat(false),\n                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 space-y-3\",\n                            children: [\n                                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: `flex ${message.isUser ? 'justify-end' : 'justify-start'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `max-w-xs p-3 rounded-lg ${message.isUser ? 'bg-blue-500 text-white' : 'glass-dark text-white'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.text\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass-dark p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-white animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-white animate-bounce delay-150\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-white animate-bounce delay-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleTextSubmit,\n                            className: \"p-4 border-t border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: inputText,\n                                        onChange: (e)=>setInputText(e.target.value),\n                                        placeholder: \"Type your message...\",\n                                        className: \"flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: !inputText.trim(),\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg px-4 py-2 text-white transition-colors\",\n                                        children: \"Send\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\main project\\\\portfolio-saurabh\\\\src\\\\components\\\\VoiceAssistant.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VoiceAssistant.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=D%3A%5Cmain%20project%5Cportfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmain%20project%5Cportfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();